<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Config;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

// Test route to verify routing is working
Route::get('/test-route', function () {
    return response()->json(['status' => 'success', 'message' => 'Routes are working properly']);
});

Route::get('/test-tenant', function () {
    try {
        tenancy()->initialize('localhost');
        $companyCount = \App\Models\CompanyData::count();
        $translatorsCount = \App\Models\Transltor::count();
        return response()->json([
            'status' => 'success',
            'tenant_initialized' => tenancy()->initialized,
            'current_tenant' => tenant() ? tenant()->id : 'none',
            'company_data_count' => $companyCount,
            'translators_count' => $translatorsCount
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage(),
            'tenant_initialized' => tenancy()->initialized
        ]);
    }
});

// Language switching route
Route::get('/lang/{locale}', function ($locale) {
    if (in_array($locale, ['ar', 'en'])) {
        session(['lang' => $locale]);
        app()->setLocale($locale);
    }
    return redirect()->back();
});

// Debug route to test language and database
Route::get('/debug-lang', function () {
    try {
        return response()->json([
            'current_locale' => app()->getLocale(),
            'session_lang' => session('lang'),
            'translators_count' => \App\Models\Transltor::count(),
            'test_translation' => trans('admin.Name'),
            'test_arabic_text' => 'الاسم'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});

// Admin login route working in central context
Route::get('/AdminLogin', function () {
    try {
        // Get company data from central database
        $Def = \App\Models\CompanyData::orderBy('id', 'desc')->first();

        return view('admin.Login', compact('Def'));
    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Failed to load admin login: ' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});

Route::post('/Login', function(\Illuminate\Http\Request $request) {
    // Process login in central context
    return app('App\Http\Controllers\AdminController')->Login($request);
});

// Add other admin routes that were in tenant.php
Route::get('/Admins', function() {
    try {
        return app('App\Http\Controllers\AdminController')->AdminsPage();
    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Failed to load Admins: ' . $e->getMessage()
        ], 500);
    }
});

// Add the main admin dashboard route
Route::get('/OstAdmin', function() {
    try {
        return view('admin.home');
    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Failed to load admin dashboard: ' . $e->getMessage()
        ], 500);
    }
});

// Add IntroView route
Route::get('/IntroView', function() {
    try {
        return app('App\Http\Controllers\AdminController')->IntroView();
    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Failed to load IntroView: ' . $e->getMessage()
        ], 500);
    }
});

// Add TermsView route
Route::get('/TermsView', function() {
    try {
        return app('App\Http\Controllers\AdminController')->TermsView();
    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Failed to load TermsView: ' . $e->getMessage()
        ], 500);
    }
});

// Add other common admin routes to prevent 404 errors
Route::get('/ReportIssue', function() {
    try {
        // Check if method exists in controller
        if (method_exists('App\Http\Controllers\AdminController', 'ReportIssue')) {
            return app('App\Http\Controllers\AdminController')->ReportIssue();
        } else {
            return response()->json(['error' => 'ReportIssue method not implemented yet'], 501);
        }
    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Failed to load ReportIssue: ' . $e->getMessage()
        ], 500);
    }
});

Route::get('/RabihEducation', function() {
    try {
        // Check if method exists in controller
        if (method_exists('App\Http\Controllers\AdminController', 'RabihEducation')) {
            return app('App\Http\Controllers\AdminController')->RabihEducation();
        } else {
            return response()->json(['error' => 'RabihEducation method not implemented yet'], 501);
        }
    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Failed to load RabihEducation: ' . $e->getMessage()
        ], 500);
    }
});

// Authentication Routes moved to tenant routes
// Route::get('AdminLogin', 'AdminController@LoginPage');
// Route::post('Login', 'AdminController@Login');
// Route::get('Logout', 'AdminController@Logout');
// Route::get('forgotpassword','AdminController@forgotpasswordPage');
// Route::post('forgotpassword','AdminController@forgotpassword');
// Route::get('reset/password/{token}','AdminController@reset_password');
// Route::post('reset/password/{token}','AdminController@reset_password_final');

// Admin Dashboard Route
Config::set('auth.defines','admin');
Route::group(['middleware' =>'Admin:admin'], function () {
    Route::group(['middleware' =>'auth:admin'], function() {
        Route::get('OstAdmin', function () {
            return view('admin.home');
        });
    });
});

Route::get('/WebSliderPage', 'WebsiteController@WebSliderPage');
Route::post('/AddWebSlider', 'WebsiteController@AddWebSlider');
Route::post('/EditWebSlider/{id}', 'WebsiteController@EditWebSlider');
Route::get('/DeleteWebSlider/{id}', 'WebsiteController@DeleteWebSlider');
Route::get('/UnActiveSlider/{id}', 'WebsiteController@UnActiveSlider');
Route::get('/ActiveSlider/{id}', 'WebsiteController@ActiveSlider');
Route::get('/AboutPage', 'WebsiteController@AboutPage');
Route::post('/UpdateAbout/{id}', 'WebsiteController@UpdateAbout');
Route::get('/SocialMediaPage', 'WebsiteController@SocialMediaPage');
Route::post('/SocialMediaUpdate/{id}', 'WebsiteController@SocialMediaUpdate');
Route::get('/MsgRqstPage', 'WebsiteController@MsgRqstPage');
Route::get('/DeleteMsgRqst/{id}', 'WebsiteController@DeleteMsgRqst');
Route::get('/ContactUSPage', 'WebsiteController@ContactUSPage');
Route::post('/ContactUSUpdate/{id}', 'WebsiteController@ContactUSUpdate');
Route::get('/ArticlesPage', 'WebsiteController@ArticlesPage');
Route::post('/AddArticles', 'WebsiteController@AddArticles');
Route::post('/EditArticles/{id}', 'WebsiteController@EditArticles');
Route::get('/DeleteArticles/{id}', 'WebsiteController@DeleteArticles');
Route::get('/TermsPage', 'WebsiteController@TermsPage');
Route::post('/UpdateTerms/{id}', 'WebsiteController@UpdateTerms');
Route::get('/PolicesPage', 'WebsiteController@PolicesPage');
Route::post('/UpdatePolices/{id}', 'WebsiteController@UpdatePolices');
Route::get('/CouponCodePage', 'WebsiteController@CouponCodePage');
Route::post('/AddCouponCode', 'WebsiteController@AddCouponCode');
Route::post('/EditCouponCode/{id}', 'WebsiteController@EditCouponCode');
Route::get('/DeleteCouponCode/{id}', 'WebsiteController@DeleteCouponCode');
Route::get('/FAQPage', 'WebsiteController@FAQPage');
Route::post('/AddFAQ', 'WebsiteController@AddFAQ');
Route::post('/EditFAQ/{id}', 'WebsiteController@EditFAQ');
Route::get('/DeleteFAQ/{id}', 'WebsiteController@DeleteFAQ');
Route::get('/CountrisPage', 'WebsiteController@CountrisPage');
Route::post('/AddCountris', 'WebsiteController@AddCountris');
Route::post('/EditCountris/{id}', 'WebsiteController@EditCountris');
Route::get('/DeleteCountris/{id}', 'WebsiteController@DeleteCountris');
Route::get('/ProDetailsImg', 'WebsiteController@ProDetailsImg');
Route::post('/EditProDetailsImg/{id}', 'WebsiteController@EditProDetailsImg');
Route::get('/BefroeFooter', 'WebsiteController@BefroeFooter');
Route::post('/EditBefroeFooter/{id}', 'WebsiteController@EditBefroeFooter');
Route::get('/ShopOrders', 'WebsiteController@ShopOrders');
Route::post('/ChangeStatusShop', 'WebsiteController@ChangeStatusShop');
Route::get('/EComDesign', 'WebsiteController@EComDesign');
Route::get('/AddMainEComDesignFirst', 'WebsiteController@AddMainEComDesignFirst');
Route::post('/AddMainEComDesign', 'WebsiteController@AddMainEComDesign');
Route::get('/AddHomeEComDesignFirst', 'WebsiteController@AddHomeEComDesignFirst');
Route::post('/AddHomeEComDesign', 'WebsiteController@AddHomeEComDesign');
Route::get('/AddHomeProductEComDesignFirst', 'WebsiteController@AddHomeProductEComDesignFirst');
